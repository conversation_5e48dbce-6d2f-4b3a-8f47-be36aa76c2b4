#!/bin/bash

# GoLinks Test Runner Script
# This script builds the service, starts it, runs tests, and cleans up

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_PORT=8067
SERVICE_ADDR=":${SERVICE_PORT}"
DATA_DIR="./test-data"
PID_FILE="./golinks.pid"

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Cleanup function
cleanup() {
    log_step "Cleaning up..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            log_info "Stopping golinks service (PID: $PID)"
            kill "$PID"
            sleep 2
            if kill -0 "$PID" 2>/dev/null; then
                log_warn "Service didn't stop gracefully, force killing"
                kill -9 "$PID"
            fi
        fi
        rm -f "$PID_FILE"
    fi
    
    # Clean up test data
    if [ -d "$DATA_DIR" ]; then
        log_info "Removing test data directory"
        rm -rf "$DATA_DIR"
    fi
}

# Set up trap to cleanup on exit
trap cleanup EXIT

# Check if service is already running
check_service() {
    if curl -s "http://localhost:${SERVICE_PORT}/healthz" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Start the service
start_service() {
    log_step "Starting golinks service..."
    
    if check_service; then
        log_warn "Service is already running on port ${SERVICE_PORT}"
        return 0
    fi
    
    # Create data directory
    mkdir -p "$DATA_DIR"
    
    # Start the service in background
    ./golinks --addr="$SERVICE_ADDR" --data="$DATA_DIR" &
    SERVICE_PID=$!
    echo "$SERVICE_PID" > "$PID_FILE"
    
    # Wait for service to start
    log_info "Waiting for service to start (PID: $SERVICE_PID)..."
    for i in {1..10}; do
        if check_service; then
            log_info "Service started successfully"
            return 0
        fi
        sleep 1
    done
    
    log_error "Service failed to start within 10 seconds"
    return 1
}

# Build the service
build_service() {
    log_step "Building golinks service..."
    
    # Build assets
    make clean
    make
    
    # Build the Go binary
    go build -o golinks .
    
    log_info "Build completed successfully"
}

# Run tests
run_tests() {
    log_step "Running tests..."
    
    # Run unit tests
    log_info "Running unit tests..."
    go test -v ./web/
    go test -v ./context/
    
    # Run integration tests
    log_info "Running integration tests..."
    go test -v integration_test.go
    
    log_info "All tests completed successfully"
}

# Main execution
main() {
    log_step "Starting GoLinks test runner"
    
    # Check if golinks binary exists, build if not
    if [ ! -f "./golinks" ]; then
        build_service
    else
        log_info "Using existing golinks binary"
    fi
    
    # Start the service
    start_service
    
    # Run tests
    run_tests
    
    log_step "Test runner completed successfully!"
}

# Handle command line arguments
case "${1:-}" in
    "build")
        build_service
        ;;
    "start")
        start_service
        log_info "Service is running. Press Ctrl+C to stop."
        wait
        ;;
    "test")
        if ! check_service; then
            log_error "Service is not running. Please start it first with: $0 start"
            exit 1
        fi
        run_tests
        ;;
    "stop")
        cleanup
        ;;
    *)
        main
        ;;
esac
