package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"
)

const (
	baseURL = "http://localhost:8067"
)

// Test data structures matching the API responses
type urlRequest struct {
	URL string `json:"url"`
}

type apiResponse struct {
	Ok bool `json:"ok"`
}

type errorResponse struct {
	Ok    bool   `json:"ok"`
	Error string `json:"error"`
}

type routeResponse struct {
	Ok    bool  `json:"ok"`
	Route route `json:"route"`
}

type route struct {
	Name string    `json:"name"`
	URL  string    `json:"url"`
	Time time.Time `json:"time"`
}

type routesResponse struct {
	Ok     bool    `json:"ok"`
	Routes []route `json:"routes"`
	Next   string  `json:"next"`
}

// Helper functions for making HTTP requests
func makeRequest(method, path string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader

	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, baseURL+path, reqBody)
	if err != nil {
		return nil, err
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	client := &http.Client{Timeout: 10 * time.Second}
	return client.Do(req)
}

func get(path string) (*http.Response, error) {
	return makeRequest("GET", path, nil)
}

func post(path string, body interface{}) (*http.Response, error) {
	return makeRequest("POST", path, body)
}

func delete(path string) (*http.Response, error) {
	return makeRequest("DELETE", path, nil)
}

// Test helper functions
func assertStatusCode(t *testing.T, resp *http.Response, expected int) {
	if resp.StatusCode != expected {
		t.Errorf("Expected status code %d, got %d", expected, resp.StatusCode)
	}
}

func decodeJSON(t *testing.T, resp *http.Response, v interface{}) {
	defer resp.Body.Close()
	if err := json.NewDecoder(resp.Body).Decode(v); err != nil {
		t.Fatalf("Failed to decode JSON response: %v", err)
	}
}

func readBody(t *testing.T, resp *http.Response) string {
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Failed to read response body: %v", err)
	}
	return string(body)
}

// Test functions
func TestHealthCheck(t *testing.T) {
	resp, err := get("/healthz")
	if err != nil {
		t.Fatalf("Failed to make health check request: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)
	body := readBody(t, resp)

	if !strings.Contains(body, "👍") {
		t.Errorf("Expected health check to return thumbs up, got: %s", body)
	}
}

func TestVersion(t *testing.T) {
	resp, err := get("/version")
	if err != nil {
		t.Fatalf("Failed to make version request: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)
	body := readBody(t, resp)

	// Version should be "none" as we built without version info
	if !strings.Contains(body, "none") {
		t.Errorf("Expected version to be 'none', got: %s", body)
	}
}

// CRUD Tests
func TestCreateURL(t *testing.T) {
	// Test creating a URL with a specific name
	testName := "test-create"
	testURL := "https://example.com"

	resp, err := post(fmt.Sprintf("/api/url/%s", testName), urlRequest{URL: testURL})
	if err != nil {
		t.Fatalf("Failed to create URL: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)

	var result routeResponse
	decodeJSON(t, resp, &result)

	if !result.Ok {
		t.Error("Expected response to be ok")
	}

	if result.Route.Name != testName {
		t.Errorf("Expected route name %s, got %s", testName, result.Route.Name)
	}

	if result.Route.URL != testURL {
		t.Errorf("Expected route URL %s, got %s", testURL, result.Route.URL)
	}

	if result.Route.Time.IsZero() {
		t.Error("Expected route time to be set")
	}
}

func TestCreateURLAutoGenerated(t *testing.T) {
	// Test creating a URL with auto-generated name
	testURL := "https://auto-generated.com"

	resp, err := post("/api/url/", urlRequest{URL: testURL})
	if err != nil {
		t.Fatalf("Failed to create URL with auto-generated name: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)

	var result routeResponse
	decodeJSON(t, resp, &result)

	if !result.Ok {
		t.Error("Expected response to be ok")
	}

	if result.Route.Name == "" {
		t.Error("Expected auto-generated name to be non-empty")
	}

	if result.Route.URL != testURL {
		t.Errorf("Expected route URL %s, got %s", testURL, result.Route.URL)
	}

	// Auto-generated names should start with ":"
	if !strings.HasPrefix(result.Route.Name, ":") {
		t.Errorf("Expected auto-generated name to start with ':', got %s", result.Route.Name)
	}
}

func TestReadURL(t *testing.T) {
	// First create a URL to read
	testName := "test-read"
	testURL := "https://read-example.com"

	// Create the URL
	resp, err := post(fmt.Sprintf("/api/url/%s", testName), urlRequest{URL: testURL})
	if err != nil {
		t.Fatalf("Failed to create URL for read test: %v", err)
	}
	assertStatusCode(t, resp, http.StatusOK)
	resp.Body.Close()

	// Now read it back
	resp, err = get(fmt.Sprintf("/api/url/%s", testName))
	if err != nil {
		t.Fatalf("Failed to read URL: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)

	var result routeResponse
	decodeJSON(t, resp, &result)

	if !result.Ok {
		t.Error("Expected response to be ok")
	}

	if result.Route.Name != testName {
		t.Errorf("Expected route name %s, got %s", testName, result.Route.Name)
	}

	if result.Route.URL != testURL {
		t.Errorf("Expected route URL %s, got %s", testURL, result.Route.URL)
	}
}

func TestReadNonExistentURL(t *testing.T) {
	// Try to read a URL that doesn't exist
	resp, err := get("/api/url/non-existent")
	if err != nil {
		t.Fatalf("Failed to make request for non-existent URL: %v", err)
	}

	assertStatusCode(t, resp, http.StatusNotFound)

	var result errorResponse
	decodeJSON(t, resp, &result)

	if result.Ok {
		t.Error("Expected response to not be ok for non-existent URL")
	}

	if result.Error == "" {
		t.Error("Expected error message for non-existent URL")
	}
}

func TestUpdateURL(t *testing.T) {
	// Test updating an existing URL (overwriting)
	testName := "test-update"
	originalURL := "https://original.com"
	updatedURL := "https://updated.com"

	// Create the original URL
	resp, err := post(fmt.Sprintf("/api/url/%s", testName), urlRequest{URL: originalURL})
	if err != nil {
		t.Fatalf("Failed to create URL for update test: %v", err)
	}
	assertStatusCode(t, resp, http.StatusOK)
	resp.Body.Close()

	// Update the URL
	resp, err = post(fmt.Sprintf("/api/url/%s", testName), urlRequest{URL: updatedURL})
	if err != nil {
		t.Fatalf("Failed to update URL: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)

	var result routeResponse
	decodeJSON(t, resp, &result)

	if !result.Ok {
		t.Error("Expected response to be ok")
	}

	if result.Route.URL != updatedURL {
		t.Errorf("Expected updated URL %s, got %s", updatedURL, result.Route.URL)
	}

	// Verify the update by reading it back
	resp, err = get(fmt.Sprintf("/api/url/%s", testName))
	if err != nil {
		t.Fatalf("Failed to read updated URL: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)

	var readResult routeResponse
	decodeJSON(t, resp, &readResult)

	if readResult.Route.URL != updatedURL {
		t.Errorf("Expected read URL to be updated to %s, got %s", updatedURL, readResult.Route.URL)
	}
}

func TestDeleteURL(t *testing.T) {
	// Test deleting a URL
	testName := "test-delete"
	testURL := "https://delete-me.com"

	// Create the URL first
	resp, err := post(fmt.Sprintf("/api/url/%s", testName), urlRequest{URL: testURL})
	if err != nil {
		t.Fatalf("Failed to create URL for delete test: %v", err)
	}
	assertStatusCode(t, resp, http.StatusOK)
	resp.Body.Close()

	// Delete the URL
	resp, err = delete(fmt.Sprintf("/api/url/%s", testName))
	if err != nil {
		t.Fatalf("Failed to delete URL: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)

	var result apiResponse
	decodeJSON(t, resp, &result)

	if !result.Ok {
		t.Error("Expected delete response to be ok")
	}

	// Verify the URL is deleted by trying to read it
	resp, err = get(fmt.Sprintf("/api/url/%s", testName))
	if err != nil {
		t.Fatalf("Failed to verify URL deletion: %v", err)
	}

	assertStatusCode(t, resp, http.StatusNotFound)
	resp.Body.Close()
}

func TestDeleteNonExistentURL(t *testing.T) {
	// Try to delete a URL that doesn't exist
	resp, err := delete("/api/url/non-existent-delete")
	if err != nil {
		t.Fatalf("Failed to make delete request for non-existent URL: %v", err)
	}

	// The API should still return 200 OK even if the URL doesn't exist
	assertStatusCode(t, resp, http.StatusOK)

	var result apiResponse
	decodeJSON(t, resp, &result)

	if !result.Ok {
		t.Error("Expected delete response to be ok even for non-existent URL")
	}
}

func TestListURLs(t *testing.T) {
	// Create several URLs for listing
	testURLs := map[string]string{
		"list-test-1": "https://list1.com",
		"list-test-2": "https://list2.com",
		"list-test-3": "https://list3.com",
	}

	// Create the URLs
	for name, url := range testURLs {
		resp, err := post(fmt.Sprintf("/api/url/%s", name), urlRequest{URL: url})
		if err != nil {
			t.Fatalf("Failed to create URL %s for list test: %v", name, err)
		}
		assertStatusCode(t, resp, http.StatusOK)
		resp.Body.Close()
	}

	// List all URLs
	resp, err := get("/api/urls/")
	if err != nil {
		t.Fatalf("Failed to list URLs: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)

	var result routesResponse
	decodeJSON(t, resp, &result)

	if !result.Ok {
		t.Error("Expected list response to be ok")
	}

	// Check that our test URLs are in the list
	foundURLs := make(map[string]bool)
	for _, route := range result.Routes {
		if _, exists := testURLs[route.Name]; exists {
			foundURLs[route.Name] = true
		}
	}

	for name := range testURLs {
		if !foundURLs[name] {
			t.Errorf("Expected to find URL %s in the list", name)
		}
	}
}

func TestListURLsWithLimit(t *testing.T) {
	// Test listing URLs with a limit parameter
	resp, err := get("/api/urls/?limit=2")
	if err != nil {
		t.Fatalf("Failed to list URLs with limit: %v", err)
	}

	assertStatusCode(t, resp, http.StatusOK)

	var result routesResponse
	decodeJSON(t, resp, &result)

	if !result.Ok {
		t.Error("Expected list response to be ok")
	}

	// Should return at most 2 results
	if len(result.Routes) > 2 {
		t.Errorf("Expected at most 2 routes with limit=2, got %d", len(result.Routes))
	}
}

// Error condition tests
func TestCreateURLWithInvalidJSON(t *testing.T) {
	// Test creating URL with invalid JSON
	resp, err := makeRequest("POST", "/api/url/invalid-json", "not json")
	if err != nil {
		t.Fatalf("Failed to make request with invalid JSON: %v", err)
	}

	assertStatusCode(t, resp, http.StatusBadRequest)

	var result errorResponse
	decodeJSON(t, resp, &result)

	if result.Ok {
		t.Error("Expected response to not be ok for invalid JSON")
	}

	if !strings.Contains(result.Error, "invalid json") {
		t.Errorf("Expected error message about invalid JSON, got: %s", result.Error)
	}
}

func TestCreateURLWithEmptyURL(t *testing.T) {
	// Test creating URL with empty URL field
	resp, err := post("/api/url/empty-url", urlRequest{URL: ""})
	if err != nil {
		t.Fatalf("Failed to create URL with empty URL: %v", err)
	}

	assertStatusCode(t, resp, http.StatusBadRequest)

	var result errorResponse
	decodeJSON(t, resp, &result)

	if result.Ok {
		t.Error("Expected response to not be ok for empty URL")
	}

	if !strings.Contains(result.Error, "url required") {
		t.Errorf("Expected error message about URL required, got: %s", result.Error)
	}
}

func TestCreateURLWithInvalidURL(t *testing.T) {
	// Test creating URL with invalid URL format
	resp, err := post("/api/url/invalid-url", urlRequest{URL: "not-a-valid-url"})
	if err != nil {
		t.Fatalf("Failed to create URL with invalid URL: %v", err)
	}

	assertStatusCode(t, resp, http.StatusBadRequest)

	var result errorResponse
	decodeJSON(t, resp, &result)

	if result.Ok {
		t.Error("Expected response to not be ok for invalid URL")
	}

	if result.Error == "" {
		t.Error("Expected error message for invalid URL")
	}
}

func TestCreateURLWithBannedName(t *testing.T) {
	// Test creating URL with a banned name (like "api")
	resp, err := post("/api/url/api", urlRequest{URL: "https://example.com"})
	if err != nil {
		t.Fatalf("Failed to create URL with banned name: %v", err)
	}

	assertStatusCode(t, resp, http.StatusBadRequest)

	var result errorResponse
	decodeJSON(t, resp, &result)

	if result.Ok {
		t.Error("Expected response to not be ok for banned name")
	}

	if !strings.Contains(result.Error, "name cannot be used") {
		t.Errorf("Expected error message about banned name, got: %s", result.Error)
	}
}

func TestReadURLWithEmptyName(t *testing.T) {
	// Test reading URL with empty name
	resp, err := get("/api/url/")
	if err != nil {
		t.Fatalf("Failed to make request with empty name: %v", err)
	}

	assertStatusCode(t, resp, http.StatusBadRequest)

	var result errorResponse
	decodeJSON(t, resp, &result)

	if result.Ok {
		t.Error("Expected response to not be ok for empty name")
	}

	if !strings.Contains(result.Error, "no name given") {
		t.Errorf("Expected error message about no name given, got: %s", result.Error)
	}
}

func TestDeleteURLWithEmptyName(t *testing.T) {
	// Test deleting URL with empty name
	resp, err := delete("/api/url/")
	if err != nil {
		t.Fatalf("Failed to make delete request with empty name: %v", err)
	}

	assertStatusCode(t, resp, http.StatusBadRequest)

	var result errorResponse
	decodeJSON(t, resp, &result)

	if result.Ok {
		t.Error("Expected response to not be ok for empty name")
	}

	if !strings.Contains(result.Error, "name required") {
		t.Errorf("Expected error message about name required, got: %s", result.Error)
	}
}

func TestUnsupportedHTTPMethods(t *testing.T) {
	// Test unsupported HTTP methods
	methods := []string{"PUT", "PATCH"}

	for _, method := range methods {
		resp, err := makeRequest(method, "/api/url/test", nil)
		if err != nil {
			t.Fatalf("Failed to make %s request: %v", method, err)
		}

		// The API should return 200 OK with an error message (as per the current implementation)
		assertStatusCode(t, resp, http.StatusOK)

		var result errorResponse
		decodeJSON(t, resp, &result)

		if result.Ok {
			t.Errorf("Expected response to not be ok for unsupported method %s", method)
		}

		if !strings.Contains(result.Error, "Method Not Allowed") {
			t.Errorf("Expected error message about method not allowed for %s, got: %s", method, result.Error)
		}
	}
}

func TestCRUDWorkflow(t *testing.T) {
	// Test a complete CRUD workflow
	testName := "crud-workflow"
	originalURL := "https://original-workflow.com"
	updatedURL := "https://updated-workflow.com"

	// 1. CREATE
	resp, err := post(fmt.Sprintf("/api/url/%s", testName), urlRequest{URL: originalURL})
	if err != nil {
		t.Fatalf("Failed to create URL in CRUD workflow: %v", err)
	}
	assertStatusCode(t, resp, http.StatusOK)
	resp.Body.Close()

	// 2. READ
	resp, err = get(fmt.Sprintf("/api/url/%s", testName))
	if err != nil {
		t.Fatalf("Failed to read URL in CRUD workflow: %v", err)
	}
	assertStatusCode(t, resp, http.StatusOK)

	var readResult routeResponse
	decodeJSON(t, resp, &readResult)

	if readResult.Route.URL != originalURL {
		t.Errorf("Expected original URL %s, got %s", originalURL, readResult.Route.URL)
	}

	// 3. UPDATE
	resp, err = post(fmt.Sprintf("/api/url/%s", testName), urlRequest{URL: updatedURL})
	if err != nil {
		t.Fatalf("Failed to update URL in CRUD workflow: %v", err)
	}
	assertStatusCode(t, resp, http.StatusOK)
	resp.Body.Close()

	// Verify update
	resp, err = get(fmt.Sprintf("/api/url/%s", testName))
	if err != nil {
		t.Fatalf("Failed to read updated URL in CRUD workflow: %v", err)
	}
	assertStatusCode(t, resp, http.StatusOK)

	var updateResult routeResponse
	decodeJSON(t, resp, &updateResult)

	if updateResult.Route.URL != updatedURL {
		t.Errorf("Expected updated URL %s, got %s", updatedURL, updateResult.Route.URL)
	}

	// 4. DELETE
	resp, err = delete(fmt.Sprintf("/api/url/%s", testName))
	if err != nil {
		t.Fatalf("Failed to delete URL in CRUD workflow: %v", err)
	}
	assertStatusCode(t, resp, http.StatusOK)
	resp.Body.Close()

	// Verify deletion
	resp, err = get(fmt.Sprintf("/api/url/%s", testName))
	if err != nil {
		t.Fatalf("Failed to verify deletion in CRUD workflow: %v", err)
	}
	assertStatusCode(t, resp, http.StatusNotFound)
	resp.Body.Close()
}
