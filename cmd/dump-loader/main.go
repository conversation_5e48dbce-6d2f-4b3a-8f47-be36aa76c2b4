package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"
)

const (
	apiPath = "http://%s:%s/api/url/%s"
)

type config struct {
	host     string
	port     string
	dumpFile string
}

type goData struct {
	url string
	ts  time.Time
}

// Route represents a single route entry
type Route struct {
	URL  string    `json:"url"`
	Time time.Time `json:"time"`
}

// RouteWithName represents a route with its name from the API response
type RouteWithName struct {
	Name string    `json:"name"`
	URL  string    `json:"url"`
	Time time.Time `json:"time"`
}

// APIResponse represents the response from /api/urls endpoint
type APIResponse struct {
	Ok     bool             `json:"ok"`
	Routes []*RouteWithName `json:"routes"`
	Next   string           `json:"next"`
}

// parseJSONData attempts to parse the JSON data in either format:
// 1. Direct map format: {"name": {"url": "...", "time": "..."}}
// 2. API response format: {"ok": true, "routes": [...]}
func parseJSONData(data []byte) (map[string]interface{}, error) {
	// First, try to parse as API response format
	var apiResp APIResponse
	if err := json.Unmarshal(data, &apiResp); err == nil && apiResp.Ok && len(apiResp.Routes) > 0 {
		// Convert API response format to expected format
		links := make(map[string]interface{})
		for _, route := range apiResp.Routes {
			links[route.Name] = map[string]interface{}{
				"url":  route.URL,
				"time": route.Time,
			}
		}
		return links, nil
	}

	// If that fails, try to parse as direct map format
	var directMap map[string]interface{}
	if err := json.Unmarshal(data, &directMap); err != nil {
		return nil, fmt.Errorf("failed to parse JSON in either format: %v", err)
	}

	// Check if this looks like a direct map format (no "ok" or "routes" keys at top level)
	if _, hasOk := directMap["ok"]; !hasOk {
		if _, hasRoutes := directMap["routes"]; !hasRoutes {
			return directMap, nil
		}
	}

	return nil, fmt.Errorf("unrecognized JSON format")
}

func main() {
	c := config{}
	flag.StringVar(&c.host, "host", "localhost", "host to post data to")
	flag.StringVar(&c.port, "port", "8067", "port on host to talk to")
	flag.StringVar(&c.dumpFile, "file", "", "dump file to load from")
	flag.Parse()

	if c.dumpFile == "" {
		log.Fatal("dump file must be specified with -file argument")
	}

	f, err := os.ReadFile(c.dumpFile)
	if err != nil {
		log.Printf("error reading dump file : %s\n", c.dumpFile)
		log.Fatal(err)
	}

	links, err := parseJSONData(f)
	if err != nil {
		log.Printf("error parsing dump file : %s\n", c.dumpFile)
		log.Fatal(err)
	}

	for k, v := range links {
		req := fmt.Sprintf(apiPath, c.host, c.port, k)
		p, err := json.Marshal(&v)
		if err != nil {
			log.Printf("error marshalling data for link : %s\n", k)
			log.Println(err)
			continue
		}
		resp, err := http.Post(req, "application/json", bytes.NewReader(p))
		if err != nil {
			log.Printf("error POSTing link : %s\n", k)
		} else {
			log.Printf("POSTed short link (%s) : %s\n", resp.Status, k)
		}
	}
}
