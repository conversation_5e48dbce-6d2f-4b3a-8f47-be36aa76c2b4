package main

import (
	"testing"
)

func TestParseJSONData_APIResponseFormat(t *testing.T) {
	// Test the API response format from /api/urls
	jsonData := `{
		"ok": true,
		"routes": [
			{
				"name": "example",
				"url": "https://example.com",
				"time": "2025-06-05T22:02:49.007045775Z"
			},
			{
				"name": "test",
				"url": "https://test.com",
				"time": "2025-06-05T22:02:50.007045775Z"
			}
		],
		"next": ""
	}`

	links, err := parseJSONData([]byte(jsonData))
	if err != nil {
		t.Fatalf("Failed to parse API response format: %v", err)
	}

	if len(links) != 2 {
		t.Fatalf("Expected 2 links, got %d", len(links))
	}

	// Check first link
	if _, exists := links["example"]; !exists {
		t.Fatal("Expected 'example' link to exist")
	}

	exampleData := links["example"].(map[string]interface{})
	if exampleData["url"] != "https://example.com" {
		t.Fatalf("Expected URL 'https://example.com', got %v", exampleData["url"])
	}

	// Check second link
	if _, exists := links["test"]; !exists {
		t.Fatal("Expected 'test' link to exist")
	}

	testData := links["test"].(map[string]interface{})
	if testData["url"] != "https://test.com" {
		t.Fatalf("Expected URL 'https://test.com', got %v", testData["url"])
	}
}

func TestParseJSONData_DirectFormat(t *testing.T) {
	// Test the direct format (legacy)
	jsonData := `{
		"example": {
			"url": "https://example.com",
			"time": "2023-01-01T00:00:00Z"
		},
		"google": {
			"url": "https://google.com",
			"time": "2023-01-01T00:00:00Z"
		}
	}`

	links, err := parseJSONData([]byte(jsonData))
	if err != nil {
		t.Fatalf("Failed to parse direct format: %v", err)
	}

	if len(links) != 2 {
		t.Fatalf("Expected 2 links, got %d", len(links))
	}

	// Check first link
	if _, exists := links["example"]; !exists {
		t.Fatal("Expected 'example' link to exist")
	}

	// Check second link
	if _, exists := links["google"]; !exists {
		t.Fatal("Expected 'google' link to exist")
	}
}

func TestParseJSONData_EmptyAPIResponse(t *testing.T) {
	// Test empty API response
	jsonData := `{
		"ok": true,
		"routes": [],
		"next": ""
	}`

	_, err := parseJSONData([]byte(jsonData))
	if err == nil {
		t.Fatal("Expected error for empty routes, but got none")
	}
}

func TestParseJSONData_InvalidJSON(t *testing.T) {
	// Test invalid JSON
	jsonData := `{invalid json}`

	_, err := parseJSONData([]byte(jsonData))
	if err == nil {
		t.Fatal("Expected error for invalid JSON, but got none")
	}
}

func TestParseJSONData_UnrecognizedFormat(t *testing.T) {
	// Test unrecognized format
	jsonData := `{
		"ok": false,
		"routes": []
	}`

	_, err := parseJSONData([]byte(jsonData))
	if err == nil {
		t.Fatal("Expected error for unrecognized format, but got none")
	}
}
