# GoLinks Testing Guide

This document describes the testing setup for the GoLinks URL shortener service.

## Overview

The testing suite includes:
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test the complete service running on localhost
- **CRUD Tests**: Comprehensive testing of Create, Read, Update, Delete operations
- **Error Handling Tests**: Test various error conditions and edge cases

## Test Files

### Unit Tests
- `web/api_test.go` - Tests for the web API handlers
- `web/web_test.go` - Tests for web functionality
- `context/context_test.go` - Tests for the data storage layer

### Integration Tests
- `integration_test.go` - Comprehensive CRUD tests against running service

### Test Runner
- `test_runner.sh` - <PERSON>rip<PERSON> to build, start service, and run all tests

## Running Tests

### Quick Test Run
To run all tests with automatic service management:
```bash
./test_runner.sh
```

### Manual Testing
1. **Build the service:**
   ```bash
   ./test_runner.sh build
   ```

2. **Start the service:**
   ```bash
   ./test_runner.sh start
   ```

3. **Run tests against running service:**
   ```bash
   ./test_runner.sh test
   ```

4. **Stop the service:**
   ```bash
   ./test_runner.sh stop
   ```

### Individual Test Suites
```bash
# Run unit tests only
go test -v ./web/
go test -v ./context/

# Run integration tests only (requires running service)
go test -v integration_test.go
```

## API Endpoints Tested

### CRUD Operations
- **CREATE**: `POST /api/url/{name}` - Create URL with specific name
- **CREATE**: `POST /api/url/` - Create URL with auto-generated name
- **READ**: `GET /api/url/{name}` - Get specific URL
- **READ**: `GET /api/urls/` - List all URLs
- **UPDATE**: `POST /api/url/{name}` - Update existing URL
- **DELETE**: `DELETE /api/url/{name}` - Delete URL

### Health & Status
- **Health Check**: `GET /healthz` - Service health status
- **Version**: `GET /version` - Service version

## Test Coverage

### Successful Operations
- ✅ Create URL with custom name
- ✅ Create URL with auto-generated name
- ✅ Read existing URL
- ✅ Update existing URL
- ✅ Delete existing URL
- ✅ List URLs with pagination
- ✅ Health check endpoint
- ✅ Version endpoint

### Error Conditions
- ✅ Read non-existent URL (404)
- ✅ Delete non-existent URL
- ✅ Create URL with invalid JSON
- ✅ Create URL with empty URL field
- ✅ Create URL with invalid URL format
- ✅ Create URL with banned name
- ✅ Read/Delete with empty name
- ✅ Unsupported HTTP methods

### Edge Cases
- ✅ Auto-generated names start with ":"
- ✅ Banned names (api, edit, healthz, links, s, version)
- ✅ URL validation (http, https, mailto, ftp schemes)
- ✅ Pagination with limits and cursors
- ✅ Complete CRUD workflow

## Manual API Testing

You can also test the API manually using curl:

### Create a URL
```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{"url":"https://example.com"}' \
  http://localhost:8067/api/url/mylink
```

### Read a URL
```bash
curl http://localhost:8067/api/url/mylink
```

### List all URLs
```bash
curl http://localhost:8067/api/urls/
```

### Update a URL
```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{"url":"https://updated-example.com"}' \
  http://localhost:8067/api/url/mylink
```

### Delete a URL
```bash
curl -X DELETE http://localhost:8067/api/url/mylink
```

### Health Check
```bash
curl http://localhost:8067/healthz
```

## Service Configuration

The test service runs with:
- **Port**: 8067 (default)
- **Data Directory**: `./test-data` (cleaned up after tests)
- **Admin Mode**: Disabled

## Test Data

Test data is stored in `./test-data` directory and is automatically cleaned up after test runs. Each test creates its own unique test data to avoid conflicts.

## Continuous Integration

The test suite is designed to be CI-friendly:
- Automatic service startup/shutdown
- Clean test data management
- Comprehensive error reporting
- Exit codes for success/failure

## Troubleshooting

### Service Won't Start
- Check if port 8067 is already in use
- Verify the binary was built successfully
- Check file permissions

### Tests Fail
- Ensure the service is running on localhost:8067
- Check network connectivity
- Verify test data directory permissions

### Port Already in Use
```bash
# Find process using port 8067
lsof -i :8067

# Kill the process if needed
kill <PID>
```
